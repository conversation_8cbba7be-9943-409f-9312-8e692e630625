'use client'

import { useState, useEffect, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { createClient } from '@/lib/supabase/client'

import { useToast } from '@/hooks/use-toast'
import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { formatCurrency } from '@/lib/utils'
import { Plus, Minus, X, Gift, Settings } from 'lucide-react'
import ImageUpload from '@/components/admin/image-upload'
import Image from 'next/image'
import { TrendingUp } from 'lucide-react'

interface Product {
  id: string
  title: string
  price: number
  discount_price?: number
  purchase_cost?: number | null
  images: string[]
}

interface BundleItem {
  product_id: string
  quantity: number
  product?: Product
}

interface Bundle {
  id: string
  title: string
  description: string
  image: string
  total_price: number
  discount_price?: number
  is_available: boolean
  gift_product_ids?: string[]
  bundle_items?: Array<{
    quantity: number
    products: Product
  }>
}

interface BundleFormProps {
  locale: string
  bundle?: Bundle
}

export default function BundleForm({ locale, bundle }: BundleFormProps) {
  const [loading, setLoading] = useState(false)
  const [products, setProducts] = useState<Product[]>([])
  const [formData, setFormData] = useState({
    title: bundle?.title || '',
    description: bundle?.description || '',
    image: bundle?.image || '',
    total_price: bundle?.total_price || 0,
    discount_price: bundle?.discount_price || 0,
    is_available: bundle?.is_available ?? true,
  })
  const [bundleItems, setBundleItems] = useState<BundleItem[]>([])
  const [giftProducts, setGiftProducts] = useState<Product[]>([])
  const [selectedGifts, setSelectedGifts] = useState<string[]>([]) // store product ids
  const [levels, setLevels] = useState<{ id: string; name: string; discount_percentage: number }[]>([])
  const [selectedLevel, setSelectedLevel] = useState<string>('none')
  const [pricesManuallyEdited, setPricesManuallyEdited] = useState({
    total_price: false,
    discount_price: false
  })

  const { toast } = useToast()
  const router = useRouter()
  const t = useTranslations('admin.bundleForm')

  useEffect(() => {
    // Load products
    const loadProducts = async () => {
      try {
        const response = await fetch('/api/admin/products')
        if (response.ok) {
          const data = await response.json()
          setProducts(data.products || [])
        }
      } catch (error) {
        console.error('Error loading products:', error)
      }
    }

    loadProducts()

    // Load loyalty levels
    const loadLevels = async () => {
      const supabase = createClient()
      const { data, error } = await supabase
        .from('user_levels')
        .select('id, name, discount_percentage')
        .order('level')
      if (!error && data) setLevels(data as { id: string; name: string; discount_percentage: number }[])
    }
    loadLevels()

    // Load bundle items if editing
    if (bundle?.bundle_items) {
      const items = bundle.bundle_items.map(item => ({
        product_id: item.products.id,
        quantity: item.quantity,
        product: item.products
      }))
      setBundleItems(items)

      // Mark prices as manually edited if editing existing bundle
      if (bundle.total_price > 0 || (bundle.discount_price && bundle.discount_price > 0)) {
        setPricesManuallyEdited({
          total_price: true,
          discount_price: true
        })
      }
    }



    // Load all available products as potential gifts
    const loadGiftProducts = async () => {
      const supabase = createClient()
      const { data: giftData, error: productsError } = await supabase
        .from('products')
        .select('id, title, price, discount_price, purchase_cost, images')
        .eq('is_available', true)
        .order('title')

      if (!productsError && giftData) setGiftProducts(giftData as Product[])
    }
    loadGiftProducts()
  }, [bundle])

  const calculateTotalPrice = useCallback(() => {
    return bundleItems.reduce((total, item) => {
      const price = item.product?.discount_price ?? item.product?.price ?? 0
      return total + price * item.quantity
    }, 0)
  }, [bundleItems])

  // Auto-populate prices when bundle items change
  useEffect(() => {
    const calculatedTotal = calculateTotalPrice()
    if (calculatedTotal > 0) {
      setFormData(prev => {
        const updates: Partial<typeof formData> = {}

        // Only update total_price if not manually edited
        if (!pricesManuallyEdited.total_price) {
          updates.total_price = calculatedTotal
        }

        // Only update discount_price if not manually edited and not already set
        if (!pricesManuallyEdited.discount_price && (!prev.discount_price || prev.discount_price === 0)) {
          updates.discount_price = Math.round(calculatedTotal * 0.9 * 100) / 100
        }

        return { ...prev, ...updates }
      })
    }
  }, [bundleItems, calculateTotalPrice, pricesManuallyEdited])

  // When level changes, update bundle price automatically
  useEffect(() => {
    if (!selectedLevel || selectedLevel === 'none') return
    const level = levels.find(l => l.id === selectedLevel)
    if (!level) return
    const total = calculateTotalPrice()
    const discounted = total * (1 - (level.discount_percentage || 0) / 100)
    setFormData(prev => ({ ...prev, discount_price: Number(discounted.toFixed(2)) }))
  }, [selectedLevel, levels, bundleItems, calculateTotalPrice])

  const addProduct = (productId: string) => {
    const product = products.find(p => p.id === productId)
    if (!product) return

    const existingItem = bundleItems.find(item => item.product_id === productId)
    if (existingItem) {
      setBundleItems(items =>
        items.map(item =>
          item.product_id === productId
            ? { ...item, quantity: item.quantity + 1 }
            : item
        )
      )
    } else {
      setBundleItems(items => [...items, {
        product_id: productId,
        quantity: 1,
        product
      }])
    }
  }

  const updateQuantity = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      setBundleItems(items => items.filter(item => item.product_id !== productId))
    } else {
      setBundleItems(items =>
        items.map(item =>
          item.product_id === productId
            ? { ...item, quantity }
            : item
        )
      )
    }
  }

  const removeProduct = (productId: string) => {
    setBundleItems(items => items.filter(item => item.product_id !== productId))
  }

  const calculateGiftsCost = () => {
    return selectedGifts.reduce((total, giftId) => {
      const gift = giftProducts.find(g => g.id === giftId)
      if (!gift) return total
      return total + (gift.purchase_cost ?? 0)
    }, 0)
  }

  const calculateTotalCost = () => {
    const productsCost = bundleItems.reduce((total, item) => {
      const cost = item.product?.purchase_cost ?? 0
      return total + cost * item.quantity
    }, 0)
    const giftsCost = calculateGiftsCost()
    return productsCost + giftsCost
  }

  const calculateSalePrice = () => {
    const basePrice = formData.discount_price > 0 ? formData.discount_price : formData.total_price
    const levelPerc = selectedLevel && selectedLevel !== 'none' ? (levels.find(l => l.id === selectedLevel)?.discount_percentage || 0) : 0
    return basePrice * (1 - levelPerc / 100)
  }

  const resetPricesToCalculated = () => {
    const calculatedTotal = calculateTotalPrice()
    if (calculatedTotal > 0) {
      setFormData(prev => ({
        ...prev,
        total_price: calculatedTotal,
        discount_price: Math.round(calculatedTotal * 0.9 * 100) / 100
      }))
      setPricesManuallyEdited({
        total_price: false,
        discount_price: false
      })
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      if (bundleItems.length === 0) {
        throw new Error(t('minOneProduct'))
      }

      const bundleData = {
        ...formData,
        items: bundleItems.map(item => ({
          product_id: item.product_id,
          quantity: item.quantity,
        })),
      }

      const url = bundle 
        ? `/api/admin/bundles/${bundle.id}` 
        : '/api/admin/bundles'
      
      const method = bundle ? 'PATCH' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bundleData),
      })

      if (response.ok) {
        toast({
          title: bundle ? t('successUpdate') : t('successCreate'),
          description: bundle
            ? t('successUpdateMessage')
            : t('successCreateMessage'),
        })
        router.push(`/${locale}/admin/bundles`)
      } else {
        const error = await response.json()
        throw new Error(error.error || t('errorMessage'))
      }
    } catch (error) {
      console.error('Error saving bundle:', error)
      toast({
        title: t('error'),
        description: error instanceof Error ? error.message : t('errorMessage'),
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  

  return (
    <div className="space-y-8">
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Basic Information Section */}
        <div className="grid lg:grid-cols-2 gap-6">
          {/* Left Column - Basic Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                {t('basicInfo')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="title">{t('title')}</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    className="mt-2"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="description">{t('description')}</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="mt-2"
                    rows={4}
                    required
                  />
                </div>

                <div>
                  <Label>{t('bundleImage')}</Label>
                  <div className="mt-2">
                    <ImageUpload
                      images={formData.image ? [formData.image] : []}
                      onImagesChange={(images: string[]) => setFormData({ ...formData, image: images[0] || '' })}
                      folder="bundles"
                      maxImages={1}
                      label={t('bundleImage')}
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Checkbox
                    id="is_available"
                    checked={formData.is_available}
                    onCheckedChange={(checked) => setFormData({ ...formData, is_available: checked as boolean })}
                  />
                  <Label htmlFor="is_available">
                    {t('isAvailable')}
                  </Label>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Right Column - Products */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                {t('addProducts')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label>{t('selectProduct')}</Label>
                  <Select onValueChange={addProduct}>
                    <SelectTrigger className="mt-2">
                      <SelectValue placeholder={t('selectProduct')} />
                    </SelectTrigger>
                    <SelectContent>
                      {products
                        .filter(product => product.id && product.id.trim() !== '' && !bundleItems.some(item => item.product_id === product.id))
                        .map((product) => (
                          <SelectItem key={product.id} value={product.id}>
                            {product.title} - {formatCurrency(product.discount_price || product.price)}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Selected Products */}
                {bundleItems.length > 0 && (
                  <div className="space-y-3">
                    <Label>{t('selectedProducts')}</Label>
                    <div className="space-y-2">
                      {bundleItems.map((item) => (
                        <div key={item.product_id} className="flex items-center gap-3 p-3 bg-muted rounded-lg">
                          {item.product?.images?.[0] && (
                            <div className="w-10 h-10 rounded overflow-hidden bg-background flex-shrink-0">
                              <Image
                                src={item.product.images[0]}
                                alt={item.product.title}
                                width={40}
                                height={40}
                                className="w-full h-full object-cover"
                              />
                            </div>
                          )}
                          <div className="flex-1 min-w-0">
                            <p className="font-medium truncate">{item.product?.title}</p>
                            <p className="text-sm text-muted-foreground">
                              {formatCurrency(item.product?.discount_price ?? item.product?.price ?? 0)} {t('perPiece')}
                            </p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => updateQuantity(item.product_id, item.quantity - 1)}
                              className="h-8 w-8 p-0"
                            >
                              <Minus className="h-3 w-3" />
                            </Button>
                            <span className="w-8 text-center text-sm font-medium">{item.quantity}</span>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => updateQuantity(item.product_id, item.quantity + 1)}
                              className="h-8 w-8 p-0"
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                            <Button
                              type="button"
                              variant="destructive"
                              size="sm"
                              onClick={() => removeProduct(item.product_id)}
                              className="h-8 w-8 p-0"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Gift Products Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Gift className="h-5 w-5" />
              {t('giftProducts')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label className="text-sm font-medium">{t('selectGiftProduct')}</Label>
                <Select onValueChange={(value) => {
                  if (value && !selectedGifts.includes(value)) {
                    setSelectedGifts([...selectedGifts, value])
                  }
                }}>
                  <SelectTrigger className="mt-2">
                    <SelectValue placeholder={t('selectGiftPlaceholder')} />
                  </SelectTrigger>
                  <SelectContent>
                    {giftProducts
                      .filter(product => !selectedGifts.includes(product.id))
                      .map((product) => (
                        <SelectItem key={product.id} value={product.id}>
                          <div className="flex items-center gap-2">
                            {product.images && product.images[0] && (
                              <Image
                                src={product.images[0]}
                                alt={product.title}
                                width={24}
                                height={24}
                                className="rounded object-cover"
                              />
                            )}
                            <span>{product.title}</span>
                            <span className="text-muted-foreground">
                              - {formatCurrency(product.discount_price || product.price)}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Selected Gifts */}
              {selectedGifts.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">{t('selectedGifts')}</Label>
                  <div className="space-y-2">
                    {selectedGifts.map((giftId) => {
                      const gift = giftProducts.find(p => p.id === giftId)
                      if (!gift) return null

                      return (
                        <div key={giftId} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                          <div className="flex items-center gap-3">
                            {gift.images && gift.images[0] && (
                              <Image
                                src={gift.images[0]}
                                alt={gift.title}
                                width={40}
                                height={40}
                                className="rounded object-cover"
                              />
                            )}
                            <div>
                              <p className="font-medium">{gift.title}</p>
                              <p className="text-sm text-muted-foreground">
                                {t('cost')}: {formatCurrency(gift.purchase_cost ?? 0)}
                              </p>
                            </div>
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => setSelectedGifts(selectedGifts.filter(id => id !== giftId))}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      )
                    })}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Pricing & Margins Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Simulazione Acquisto & Margini
            </CardTitle>
            <p className="text-muted-foreground">Calcola i tuoi profitti e definisci il prezzo ottimale</p>
          </CardHeader>
          <CardContent>
            <div className="grid lg:grid-cols-2 gap-6">
              {/* Pricing */}
              <div className="space-y-4">
                <div className="flex items-center justify-between border-b pb-2">
                  <h3 className="text-lg font-semibold">
                    Prezzi del Bundle
                  </h3>
                  {bundleItems.length > 0 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={resetPricesToCalculated}
                      className="text-xs"
                    >
                      Ricalcola Automaticamente
                    </Button>
                  )}
                </div>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="total_price" className="flex items-center gap-2">
                      {t('totalPrice')} *
                      {!pricesManuallyEdited.total_price && bundleItems.length > 0 && (
                        <span className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
                          Auto-calcolato
                        </span>
                      )}
                    </Label>
                    <Input
                      id="total_price"
                      type="number"
                      step="0.01"
                      value={formData.total_price}
                      onChange={(e) => {
                        setFormData({ ...formData, total_price: parseFloat(e.target.value) || 0 })
                        setPricesManuallyEdited(prev => ({ ...prev, total_price: true }))
                      }}
                      className="mt-2 text-lg font-semibold"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="discount_price" className="flex items-center gap-2">
                      {t('discountPrice')} ({t('optional')})
                      {!pricesManuallyEdited.discount_price && bundleItems.length > 0 && formData.discount_price > 0 && (
                        <span className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
                          Auto-calcolato (-10%)
                        </span>
                      )}
                    </Label>
                    <Input
                      id="discount_price"
                      type="number"
                      step="0.01"
                      value={formData.discount_price}
                      onChange={(e) => {
                        setFormData({ ...formData, discount_price: parseFloat(e.target.value) || 0 })
                        setPricesManuallyEdited(prev => ({ ...prev, discount_price: true }))
                      }}
                      className="mt-2"
                    />
                  </div>

                  {/* Loyalty Level Discount */}
                  <div>
                    <Label htmlFor="loyalty_level">
                      Simulazione Livello Fedeltà
                    </Label>
                    <Select value={selectedLevel} onValueChange={setSelectedLevel}>
                      <SelectTrigger className="mt-2">
                        <SelectValue placeholder="Seleziona livello per simulazione" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">Nessun sconto fedeltà</SelectItem>
                        {levels.map((level) => (
                          <SelectItem key={level.id} value={level.id}>
                            {level.name} (-{level.discount_percentage}%)
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Cost Calculation */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold border-b pb-2">
                  Analisi Costi e Margini
                </h3>

                <div className="space-y-3 bg-muted rounded-lg p-4">
                  <div className="flex justify-between items-center">
                    <span>Costo Prodotti:</span>
                    <span className="font-semibold">{formatCurrency(calculateTotalCost())}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Costo Regali:</span>
                    <span className="font-semibold">{formatCurrency(calculateGiftsCost())}</span>
                  </div>

                  <hr />
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Costo Totale:</span>
                    <span className="font-bold">
                      {formatCurrency(calculateTotalCost())}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Prezzo Vendita:</span>
                    <span className="font-bold text-primary">
                      {formatCurrency(calculateSalePrice())}
                    </span>
                  </div>
                  <hr />
                  <div className="flex justify-between items-center bg-primary/10 p-3 rounded-lg">
                    <span className="font-bold">Margine Netto:</span>
                    <span className="font-bold text-xl text-primary">
                      {(() => {
                        const totalCost = calculateTotalCost();
                        const salePrice = calculateSalePrice();
                        const margin = salePrice - totalCost;
                        const marginPercentage = salePrice > 0 ? (margin / salePrice) * 100 : 0;
                        return `${formatCurrency(margin)} (${marginPercentage.toFixed(1)}%)`;
                      })()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>



        {/* Action Buttons */}
        <div className="flex gap-4 pt-6">
          <Button
            type="submit"
            disabled={loading || bundleItems.length === 0}
          >
            {loading ? (bundle ? t('updating') : t('creating')) : (bundle ? t('update') : t('create'))}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push(`/${locale}/admin/bundles`)}
          >
            {t('cancel')}
          </Button>
        </div>
      </form>
    </div>
  )
}
